{"source_dir": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide", "name": "moonbit-website-guide", "packages": [{"is-main": true, "is-third-party": false, "root-path": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide", "root": "moonbit-website-guide", "rel": "", "files": {"E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide\\main.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}, "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide\\simple.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}, "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide\\test.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}}, "wbtest-files": {}, "test-files": {}, "mbt-md-files": {}, "deps": [], "wbtest-deps": [], "test-deps": [], "artifact": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\moonbit-website-guide\\target\\wasm-gc\\release\\check\\moonbit-website-guide.mi"}], "deps": [], "backend": "wasm-gc", "opt_level": "release", "source": null}