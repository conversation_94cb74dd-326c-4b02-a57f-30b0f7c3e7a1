#!/usr/bin/env python3
"""
简单的HTTP服务器，用于预览MoonBit网站指南界面
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server(port=8080):
    """启动HTTP服务器"""
    
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 创建服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🚀 MoonBit 网站指南预览服务器启动成功!")
            print(f"📱 服务器地址: http://localhost:{port}")
            print(f"📁 服务目录: {script_dir}")
            print()
            print("📋 可用页面:")
            print(f"   🖥️  桌面版: http://localhost:{port}/preview.html")
            print(f"   📱 移动版: http://localhost:{port}/mobile-preview.html")
            print(f"   📄 原始版: http://localhost:{port}/output.html")
            print()
            print("⚡ 功能特性:")
            print("   ✅ 交互式进度跟踪")
            print("   ✅ 实时成本计算器")
            print("   ✅ 收入预估器")
            print("   ✅ 三阶段实施指南")
            print("   ✅ 技术对比分析")
            print()
            print("🌙 MoonBit 优势:")
            print("   ✅ 类型安全")
            print("   ✅ 函数式编程")
            print("   ✅ 编译时错误检查")
            print("   ✅ 不可变状态管理")
            print()
            print("按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}/preview.html')
                print(f"🌐 已自动打开浏览器: http://localhost:{port}/preview.html")
            except:
                print("⚠️  无法自动打开浏览器，请手动访问上述地址")
            
            print("-" * 50)
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    # 检查可用的HTML文件
    script_dir = Path(__file__).parent
    html_files = list(script_dir.glob("*.html"))
    
    if not html_files:
        print("❌ 未找到HTML文件，请确保在正确的目录中运行此脚本")
        sys.exit(1)
    
    print("🌙 MoonBit 网站建设指南 - 预览服务器")
    print("=" * 50)
    
    # 显示可用文件
    print("📁 发现的HTML文件:")
    for html_file in html_files:
        print(f"   📄 {html_file.name}")
    print()
    
    # 启动服务器
    start_server()
