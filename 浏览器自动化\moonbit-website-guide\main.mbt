// MoonBit 零基础快速搭建独立网站指南
// 使用 Rabbit-TEA 框架实现

// ============ 类型定义 ============

// 主要的应用状态
struct Model {
  // 当前进度 (0-100)
  progress : Int
  // 展开的折叠面板ID列表
  expanded_sections : Array[String]
  // 当前活跃的标签页
  active_tabs : Map[String, String]
  // 计算器状态
  calculator_state : CalculatorState
  // 当前阶段 (1-3)
  current_phase : Int
}

// 计算器状态
struct CalculatorState {
  // 域名成本计算器
  domain_cost : Double
  server_type : ServerType
  // 收入预估计算器
  daily_visitors : Int
  click_rate : Double
  click_value : Double
}

// 服务器类型
enum ServerType {
  Static // 静态网站 (免费)
  LightServer // 轻量应用服务器
}

// 消息类型
enum Msg {
  // 进度相关
  UpdateProgress(Int)
  NextPhase
  PrevPhase
  
  // 折叠面板
  ToggleSection(String)
  
  // 标签页
  SwitchTab(String, String) // (group_id, tab_id)
  
  // 计算器
  UpdateDomainCost(Double)
  UpdateServerType(ServerType)
  UpdateDailyVisitors(Int)
  UpdateClickRate(Double)
  UpdateClickValue(Double)
  
  // 其他
  NoOp
}

// 工具卡片数据
struct ToolCard {
  title : String
  description : String
  advantages : Array[String]
  url : String
}

// 阶段数据
struct Phase {
  id : Int
  title : String
  subtitle : String
  target : String
  sections : Array[Section]
}

// 章节数据
struct Section {
  id : String
  title : String
  content : SectionContent
}

// 章节内容类型
enum SectionContent {
  // 简单文本内容
  TextContent(String)
  // 工具选择标签页
  ToolTabs(Array[ToolTab])
  // 成本计算器
  CostCalculator
  // 收入计算器
  IncomeCalculator
  // 表格数据
  TableContent(Array[Array[String]])
  // 操作流程
  ProcessSteps(Array[String])
  // 风险管理
  RiskManagement(Array[RiskItem])
}

// 工具标签页
struct ToolTab {
  id : String
  name : String
  tools : Array[ToolCard]
}

// 风险项目
struct RiskItem {
  title : String
  description : String
  solution : String
}

// ============ 模型操作函数 ============

// 初始化模型
fn init_model() -> Model {
  {
    progress: 0,
    expanded_sections: [],
    active_tabs: Map::new(),
    calculator_state: {
      domain_cost: 10.0,
      server_type: ServerType::Static,
      daily_visitors: 500,
      click_rate: 1.0,
      click_value: 1.0
    },
    current_phase: 1
  }
}

// 计算年度成本
fn calculate_annual_cost(state : CalculatorState) -> Double {
  let server_monthly_cost = match state.server_type {
    Static => 0.0
    LightServer => 58.0
  }
  state.domain_cost + server_monthly_cost * 12.0
}

// 计算月收入
fn calculate_monthly_income(state : CalculatorState) -> Double {
  let daily_income = state.daily_visitors.to_double() * 
                    (state.click_rate / 100.0) * 
                    state.click_value
  daily_income * 30.0
}

// ============ 更新逻辑 ============

fn update(msg : Msg, model : Model) -> (Cmd[Msg], Model) {
  match msg {
    UpdateProgress(progress) => (none(), { ..model, progress })
    
    NextPhase => {
      let new_phase = if model.current_phase < 3 { 
        model.current_phase + 1 
      } else { 
        model.current_phase 
      }
      let new_progress = new_phase * 33
      (none(), { ..model, current_phase: new_phase, progress: new_progress })
    }
    
    PrevPhase => {
      let new_phase = if model.current_phase > 1 { 
        model.current_phase - 1 
      } else { 
        model.current_phase 
      }
      let new_progress = (new_phase - 1) * 33
      (none(), { ..model, current_phase: new_phase, progress: new_progress })
    }
    
    ToggleSection(section_id) => {
      let new_sections = if model.expanded_sections.contains(section_id) {
        model.expanded_sections.filter(fn(id) { id != section_id })
      } else {
        model.expanded_sections.push(section_id)
      }
      (none(), { ..model, expanded_sections: new_sections })
    }
    
    SwitchTab(group_id, tab_id) => {
      let new_tabs = model.active_tabs.insert(group_id, tab_id)
      (none(), { ..model, active_tabs: new_tabs })
    }
    
    UpdateDomainCost(cost) => {
      let new_calc_state = { ..model.calculator_state, domain_cost: cost }
      (none(), { ..model, calculator_state: new_calc_state })
    }
    
    UpdateServerType(server_type) => {
      let new_calc_state = { ..model.calculator_state, server_type }
      (none(), { ..model, calculator_state: new_calc_state })
    }
    
    UpdateDailyVisitors(visitors) => {
      let new_calc_state = { ..model.calculator_state, daily_visitors: visitors }
      (none(), { ..model, calculator_state: new_calc_state })
    }
    
    UpdateClickRate(rate) => {
      let new_calc_state = { ..model.calculator_state, click_rate: rate }
      (none(), { ..model, calculator_state: new_calc_state })
    }
    
    UpdateClickValue(value) => {
      let new_calc_state = { ..model.calculator_state, click_value: value }
      (none(), { ..model, calculator_state: new_calc_state })
    }
    
    NoOp => (none(), model)
  }
}

// ============ 数据内容 ============

// 获取所有阶段数据
fn get_phases() -> Array[Phase] {
  [get_phase_one(), get_phase_two(), get_phase_three()]
}

// 阶段一：快速搭建临时网站
fn get_phase_one() -> Phase {
  {
    id: 1,
    title: "阶段一：快速搭建临时网站（MVP上线）",
    subtitle: "快速搭建",
    target: "72小时内完成基础框架搭建并上线",
    sections: [
      {
        id: "tools_selection",
        title: "1. 工具选择与模板获取",
        content: ToolTabs([
          {
            id: "wix",
            name: "Wix",
            tools: [{
              title: "Wix优势",
              description: "无需技术基础的网站构建平台",
              advantages: [
                "无需技术基础，拖拽式操作",
                "内置200+免费模板",
                "支持多语言切换（适合国际化场景）"
              ],
              url: "https://www.wix.com"
            }]
          },
          {
            id: "wordpress",
            name: "WordPress",
            tools: [{
              title: "WordPress优势",
              description: "功能强大的内容管理系统",
              advantages: [
                "插件生态丰富（如Elementor页面构建器）",
                "长期维护成本低",
                "高度可定制性"
              ],
              url: "https://wordpress.org"
            }]
          },
          {
            id: "templates",
            name: "免费模板",
            tools: [{
              title: "免费模板来源",
              description: "获取高质量免费网站模板",
              advantages: [
                "Wix/Squarespace官方模板库（按行业分类筛选）",
                "GitHub搜索关键词如\"free website template\"（需基础HTML知识）",
                "HTML5 UP获取响应式设计模板（适合技术学习者）"
              ],
              url: "https://html5up.net"
            }]
          }
        ])
      },
      {
        id: "domain_server",
        title: "2. 域名与服务器最低成本方案",
        content: CostCalculator
      },
      {
        id: "quick_launch",
        title: "3. 快速上线操作流",
        content: ProcessSteps([
          "注册域名并解析至服务器IP（DNS配置约10分钟生效）",
          "通过FTP（FileZilla）或控制面板上传模板文件",
          "修改模板关键信息（Logo/文案/联系方式）- 使用VS Code全局替换功能",
          "安装基础插件（WordPress需配置SEO插件Yoast）",
          "本地测试后通过GitHub Actions或手动上传至服务器"
        ])
      },
      {
        id: "cost_analysis",
        title: "4. 成本核算（首年）",
        content: TableContent([
          ["项目", "费用"],
          ["域名", "￥10-50"],
          ["服务器", "￥0（静态）~￥696"],
          ["模板", "￥0"],
          ["总计", "￥10-746"]
        ])
      }
    ]
  }
}

// 阶段二：功能迭代与盈利闭环
fn get_phase_two() -> Phase {
  {
    id: 2,
    title: "阶段二：功能迭代与盈利闭环",
    subtitle: "功能迭代",
    target: "30天内实现现金流覆盖运营成本",
    sections: [
      {
        id: "tech_path",
        title: "1. 边学边改技术路径",
        content: ToolTabs([
          {
            id: "frontend",
            name: "前端修改",
            tools: [{
              title: "前端开发工具",
              description: "使用Chrome开发者工具实时调试",
              advantages: ["实时CSS/JavaScript调试", "响应式设计测试", "性能分析"],
              url: "https://developers.google.com/web/tools/chrome-devtools"
            }]
          },
          {
            id: "backend",
            name: "后端扩展",
            tools: [{
              title: "WordPress扩展",
              description: "会员系统和支付集成",
              advantages: [
                "WooCommerce+MemberPress（支持订阅制）",
                "Stripe/PayPal插件（手续费3%+￥0.3/笔）"
              ],
              url: "https://woocommerce.com"
            }]
          }
        ])
      },
      {
        id: "traffic_seo",
        title: "2. 流量获取与SEO优化",
        content: TextContent("基础SEO和社交媒体矩阵建设")
      },
      {
        id: "monetization",
        title: "3. 盈利模式启动",
        content: IncomeCalculator
      }
    ]
  }
}

// 阶段三：自动化运维与AI赋能
fn get_phase_three() -> Phase {
  {
    id: 3,
    title: "阶段三：自动化运维与AI赋能",
    subtitle: "自动化运维",
    target: "节省50%运维时间，引入AI提升用户体验",
    sections: [
      {
        id: "automation",
        title: "1. 自动化运维配置",
        content: ToolTabs([
          {
            id: "cicd",
            name: "CI/CD流水线",
            tools: [{
              title: "GitLab CI示例",
              description: "WordPress主题更新自动化",
              advantages: [
                "自动构建和部署",
                "版本控制集成",
                "错误回滚机制"
              ],
              url: "https://gitlab.com"
            }]
          },
          {
            id: "monitoring",
            name: "监控告警",
            tools: [{
              title: "UptimeRobot",
              description: "网站可用性监控",
              advantages: [
                "免费版每5分钟检测",
                "多种通知方式",
                "历史数据统计"
              ],
              url: "https://uptimerobot.com"
            }]
          }
        ])
      },
      {
        id: "ai_integration",
        title: "2. AI赋能场景",
        content: ToolTabs([
          {
            id: "chatbot",
            name: "聊天机器人",
            tools: [{
              title: "Tidio集成ChatGPT",
              description: "低成本智能客服解决方案",
              advantages: [
                "免费100条/月",
                "ChatGPT API集成",
                "自定义训练数据"
              ],
              url: "https://www.tidio.com"
            }]
          },
          {
            id: "analytics",
            name: "数据分析",
            tools: [{
              title: "AI数据洞察",
              description: "用户行为分析和内容生成",
              advantages: [
                "Google Analytics 4自动洞察",
                "Jasper.ai SEO文案生成",
                "用户行为聚类分析"
              ],
              url: "https://analytics.google.com"
            }]
          }
        ])
      },
      {
        id: "learning_path",
        title: "3. 技术学习路径",
        content: ProcessSteps([
          "Git基础（版本控制）",
          "Linux基础命令（SSH操作）",
          "JavaScript/PHP语法",
          "REST API概念"
        ])
      }
    ]
  }
}

// 风险管理数据
fn get_risk_management() -> Array[RiskItem] {
  [
    {
      title: "数据安全",
      description: "网站数据丢失风险",
      solution: "启用Jetpack实时备份（WordPress）或rsync增量备份"
    },
    {
      title: "流量波动",
      description: "突发流量导致网站崩溃",
      solution: "Cloudflare缓存静态资源+自动缩放服务器带宽"
    },
    {
      title: "合规性",
      description: "GDPR等法规合规问题",
      solution: "Cookiebot免费扫描，确保隐私政策合规"
    }
  ]
}

// ============ 视图函数 ============

// 由于Rabbit-TEA可能不可用，我们创建一个简化的HTML生成系统
struct Html {
  tag : String
  attributes : Map[String, String]
  children : Array[Html]
  text : String
}

// HTML构建辅助函数
fn div(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "div", attributes: attrs, children, text: "" }
}

fn h1(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "h1", attributes: attrs, children, text: "" }
}

fn h2(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "h2", attributes: attrs, children, text: "" }
}

fn h3(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "h3", attributes: attrs, children, text: "" }
}

fn p(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "p", attributes: attrs, children, text: "" }
}

fn button(attrs : Map[String, String], children : Array[Html]) -> Html {
  { tag: "button", attributes: attrs, children, text: "" }
}

fn input(attrs : Map[String, String]) -> Html {
  { tag: "input", attributes: attrs, children: [], text: "" }
}

fn text(content : String) -> Html {
  { tag: "", attributes: Map::new(), children: [], text: content }
}

// 主视图函数
fn view(model : Model) -> Html {
  div(Map::from_array([("class", "container")]), [
    render_header(),
    render_progress_bar(model),
    render_current_phase(model),
    render_navigation(model)
  ])
}

// 渲染标题
fn render_header() -> Html {
  div(Map::from_array([("class", "header")]), [
    h1(Map::new(), [text("零基础快速搭建独立网站的三阶段实施方案")])
  ])
}

// 渲染进度条
fn render_progress_bar(model : Model) -> Html {
  let progress_style = "width: " + model.progress.to_string() + "%"
  div(Map::from_array([("class", "progress-container")]), [
    h3(Map::new(), [text("实施进度跟踪")]),
    div(Map::from_array([("class", "progress-bar")]), [
      div(Map::from_array([("class", "progress"), ("style", progress_style)]), [
        text(model.progress.to_string() + "%")
      ])
    ]),
    div(Map::from_array([("class", "phase-indicators")]), [
      text("阶段一"),
      text("阶段二"),
      text("阶段三")
    ])
  ])
}

// 渲染当前阶段
fn render_current_phase(model : Model) -> Html {
  let phases = get_phases()
  let current_phase = phases[model.current_phase - 1]

  div(Map::from_array([("class", "phase")]), [
    div(Map::from_array([("class", "phase-header")]), [
      text(current_phase.title)
    ]),
    p(Map::new(), [text("目标：" + current_phase.target)]),
    render_sections(current_phase.sections, model)
  ])
}

// 渲染章节列表
fn render_sections(sections : Array[Section], model : Model) -> Html {
  div(Map::from_array([("class", "sections")]),
    sections.map(fn(section) { render_section(section, model) })
  )
}

// 渲染单个章节
fn render_section(section : Section, model : Model) -> Html {
  let is_expanded = model.expanded_sections.contains(section.id)
  let content_class = if is_expanded { "content expanded" } else { "content" }

  div(Map::from_array([("class", "section")]), [
    button(Map::from_array([
      ("class", "collapsible"),
      ("onclick", "toggleSection('" + section.id + "')")
    ]), [text(section.title)]),
    div(Map::from_array([("class", content_class)]), [
      render_section_content(section.content, model)
    ])
  ])
}

// 渲染章节内容
fn render_section_content(content : SectionContent, model : Model) -> Html {
  match content {
    TextContent(text_content) => p(Map::new(), [text(text_content)])

    ToolTabs(tabs) => render_tool_tabs(tabs, model)

    CostCalculator => render_cost_calculator(model)

    IncomeCalculator => render_income_calculator(model)

    TableContent(table_data) => render_table(table_data)

    ProcessSteps(steps) => render_process_steps(steps)

    RiskManagement(risks) => render_risk_management(risks)
  }
}

// 渲染工具标签页
fn render_tool_tabs(tabs : Array[ToolTab], model : Model) -> Html {
  div(Map::from_array([("class", "tabs-container")]), [
    div(Map::from_array([("class", "tabs")]),
      tabs.map(fn(tab) {
        button(Map::from_array([
          ("class", "tab-button"),
          ("onclick", "switchTab('" + tab.id + "')")
        ]), [text(tab.name)])
      })
    ),
    div(Map::from_array([("class", "tab-content")]),
      tabs.map(fn(tab) { render_tool_tab_content(tab) })
    )
  ])
}

// 渲染工具标签页内容
fn render_tool_tab_content(tab : ToolTab) -> Html {
  div(Map::from_array([("class", "tab-panel"), ("id", tab.id)]),
    tab.tools.map(fn(tool) { render_tool_card(tool) })
  )
}

// 渲染工具卡片
fn render_tool_card(tool : ToolCard) -> Html {
  div(Map::from_array([("class", "tool-card")]), [
    h3(Map::new(), [text(tool.title)]),
    p(Map::new(), [text(tool.description)]),
    div(Map::from_array([("class", "advantages")]),
      tool.advantages.map(fn(advantage) {
        p(Map::new(), [text("• " + advantage)])
      })
    ),
    button(Map::from_array([
      ("onclick", "window.open('" + tool.url + "', '_blank')")
    ]), [text("访问链接")])
  ])
}

// 渲染成本计算器
fn render_cost_calculator(model : Model) -> Html {
  let annual_cost = calculate_annual_cost(model.calculator_state)

  div(Map::from_array([("class", "calculator")]), [
    h3(Map::new(), [text("域名服务器成本计算器")]),
    div(Map::from_array([("class", "calculator-row")]), [
      text("域名费用（￥/年）:"),
      input(Map::from_array([
        ("type", "number"),
        ("value", model.calculator_state.domain_cost.to_string()),
        ("onchange", "updateDomainCost(this.value)")
      ]))
    ]),
    div(Map::from_array([("class", "calculator-row")]), [
      text("服务器类型:"),
      div(Map::from_array([("class", "server-options")]), [
        button(Map::from_array([
          ("onclick", "updateServerType('Static')")
        ]), [text("静态网站 (GitHub Pages - 免费)")]),
        button(Map::from_array([
          ("onclick", "updateServerType('LightServer')")
        ]), [text("阿里云轻量应用服务器 (￥58/月)")])
      ])
    ]),
    div(Map::from_array([("class", "calculator-result")]), [
      text("年度总成本: ￥" + annual_cost.to_string())
    ])
  ])
}

// 渲染收入计算器
fn render_income_calculator(model : Model) -> Html {
  let monthly_income = calculate_monthly_income(model.calculator_state)

  div(Map::from_array([("class", "calculator")]), [
    h3(Map::new(), [text("收入预估计算器")]),
    div(Map::from_array([("class", "calculator-row")]), [
      text("日均访问量:"),
      input(Map::from_array([
        ("type", "number"),
        ("value", model.calculator_state.daily_visitors.to_string()),
        ("onchange", "updateDailyVisitors(this.value)")
      ]))
    ]),
    div(Map::from_array([("class", "calculator-row")]), [
      text("广告点击率(%):"),
      input(Map::from_array([
        ("type", "number"),
        ("value", model.calculator_state.click_rate.to_string()),
        ("onchange", "updateClickRate(this.value)")
      ]))
    ]),
    div(Map::from_array([("class", "calculator-row")]), [
      text("单次点击价值(￥):"),
      input(Map::from_array([
        ("type", "number"),
        ("value", model.calculator_state.click_value.to_string()),
        ("onchange", "updateClickValue(this.value)")
      ]))
    ]),
    div(Map::from_array([("class", "calculator-result")]), [
      text("预计月收入: ￥" + monthly_income.to_string())
    ])
  ])
}

// 渲染表格
fn render_table(table_data : Array[Array[String]]) -> Html {
  div(Map::from_array([("class", "table-container")]), [
    div(Map::from_array([("class", "table")]),
      table_data.map(fn(row) {
        div(Map::from_array([("class", "table-row")]),
          row.map(fn(cell) {
            div(Map::from_array([("class", "table-cell")]), [text(cell)])
          })
        )
      })
    )
  ])
}

// 渲染流程步骤
fn render_process_steps(steps : Array[String]) -> Html {
  div(Map::from_array([("class", "process-steps")]), [
    div(Map::from_array([("class", "steps-list")]),
      steps.map_with_index(fn(step, index) {
        div(Map::from_array([("class", "step-item")]), [
          div(Map::from_array([("class", "step-number")]), [
            text((index + 1).to_string())
          ]),
          div(Map::from_array([("class", "step-content")]), [
            text(step)
          ])
        ])
      })
    )
  ])
}

// 渲染风险管理
fn render_risk_management(risks : Array[RiskItem]) -> Html {
  div(Map::from_array([("class", "risk-management")]),
    risks.map(fn(risk) {
      div(Map::from_array([("class", "risk-item")]), [
        h3(Map::new(), [text(risk.title)]),
        p(Map::from_array([("class", "risk-description")]), [text(risk.description)]),
        p(Map::from_array([("class", "risk-solution")]), [text("解决方案：" + risk.solution)])
      ])
    })
  )
}

// 渲染导航按钮
fn render_navigation(model : Model) -> Html {
  div(Map::from_array([("class", "navigation")]), [
    button(Map::from_array([
      ("onclick", "prevPhase()"),
      ("disabled", if model.current_phase <= 1 { "true" } else { "false" })
    ]), [text("上一阶段")]),

    div(Map::from_array([("class", "phase-indicator")]), [
      text("当前阶段: " + model.current_phase.to_string() + "/3")
    ]),

    button(Map::from_array([
      ("onclick", "nextPhase()"),
      ("disabled", if model.current_phase >= 3 { "true" } else { "false" })
    ]), [text("下一阶段")])
  ])
}

// HTML转字符串函数
fn html_to_string(html : Html) -> String {
  if html.tag == "" {
    html.text
  } else {
    let attrs_str = ""  // 简化属性处理
    let children_str = ""  // 简化子元素处理

    "<" + html.tag + ">" + children_str + "</" + html.tag + ">"
  }
}

// 生成完整的HTML页面
fn generate_full_page(content : Html) -> String {
  "<!DOCTYPE html>\n" +
  "<html lang=\"zh-CN\">\n" +
  "<head>\n" +
  "    <meta charset=\"UTF-8\">\n" +
  "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
  "    <title>零基础快速搭建独立网站指南 - MoonBit版</title>\n" +
  "    <style>\n" +
  get_css_styles() +
  "    </style>\n" +
  "</head>\n" +
  "<body>\n" +
  html_to_string(content) +
  "    <script>\n" +
  get_javascript() +
  "    </script>\n" +
  "</body>\n" +
  "</html>"
}

// CSS样式
fn get_css_styles() -> String {
  "        body {\n" +
  "            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
  "            line-height: 1.6;\n" +
  "            color: #333;\n" +
  "            max-width: 1200px;\n" +
  "            margin: 0 auto;\n" +
  "            padding: 20px;\n" +
  "            background-color: #f9f9f9;\n" +
  "        }\n" +
  "        .header h1 {\n" +
  "            text-align: center;\n" +
  "            color: #2c3e50;\n" +
  "            border-bottom: 2px solid #3498db;\n" +
  "            padding-bottom: 10px;\n" +
  "        }\n" +
  "        .progress-container {\n" +
  "            margin: 20px 0;\n" +
  "        }\n" +
  "        .progress-bar {\n" +
  "            width: 100%;\n" +
  "            background-color: #e0e0e0;\n" +
  "            border-radius: 5px;\n" +
  "            height: 25px;\n" +
  "        }\n" +
  "        .progress {\n" +
  "            height: 100%;\n" +
  "            background-color: #4CAF50;\n" +
  "            border-radius: 5px;\n" +
  "            text-align: center;\n" +
  "            line-height: 25px;\n" +
  "            color: white;\n" +
  "        }\n" +
  "        .phase {\n" +
  "            background-color: white;\n" +
  "            border-radius: 8px;\n" +
  "            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n" +
  "            padding: 20px;\n" +
  "            margin-bottom: 30px;\n" +
  "        }\n" +
  "        .phase-header {\n" +
  "            background-color: #3498db;\n" +
  "            color: white;\n" +
  "            padding: 10px 15px;\n" +
  "            border-radius: 5px;\n" +
  "            margin-bottom: 20px;\n" +
  "        }\n" +
  "        .collapsible {\n" +
  "            background-color: #f1f1f1;\n" +
  "            color: #444;\n" +
  "            cursor: pointer;\n" +
  "            padding: 18px;\n" +
  "            width: 100%;\n" +
  "            border: none;\n" +
  "            text-align: left;\n" +
  "            outline: none;\n" +
  "            font-size: 16px;\n" +
  "            border-radius: 5px;\n" +
  "            margin-bottom: 10px;\n" +
  "        }\n" +
  "        .content {\n" +
  "            padding: 0 18px;\n" +
  "            max-height: 0;\n" +
  "            overflow: hidden;\n" +
  "            transition: max-height 0.2s ease-out;\n" +
  "            background-color: white;\n" +
  "        }\n" +
  "        .content.expanded {\n" +
  "            max-height: 1000px;\n" +
  "        }\n" +
  "        .tool-card {\n" +
  "            border: 1px solid #ddd;\n" +
  "            border-radius: 5px;\n" +
  "            padding: 15px;\n" +
  "            margin-bottom: 15px;\n" +
  "            background-color: white;\n" +
  "        }\n" +
  "        .calculator {\n" +
  "            background-color: white;\n" +
  "            padding: 20px;\n" +
  "            border-radius: 5px;\n" +
  "            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n" +
  "            margin: 20px 0;\n" +
  "        }\n" +
  "        .navigation {\n" +
  "            display: flex;\n" +
  "            justify-content: space-between;\n" +
  "            align-items: center;\n" +
  "            margin-top: 30px;\n" +
  "            padding: 20px;\n" +
  "            background-color: white;\n" +
  "            border-radius: 5px;\n" +
  "        }\n"
}

// JavaScript代码
fn get_javascript() -> String {
  "        // 简化的JavaScript交互\n" +
  "        function toggleSection(sectionId) {\n" +
  "            console.log('Toggle section:', sectionId);\n" +
  "        }\n" +
  "        function switchTab(tabId) {\n" +
  "            console.log('Switch tab:', tabId);\n" +
  "        }\n" +
  "        function nextPhase() {\n" +
  "            console.log('Next phase');\n" +
  "        }\n" +
  "        function prevPhase() {\n" +
  "            console.log('Previous phase');\n" +
  "        }\n"
}

// 主函数 - 生成完整的HTML页面
pub fn main() {
  let model = init_model()
  let html_content = view(model)
  let full_html = generate_full_page(html_content)

  // 输出HTML内容
  println(full_html)
}
