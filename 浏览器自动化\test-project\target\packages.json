{"source_dir": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project", "name": "username/hello", "packages": [{"is-main": false, "is-third-party": false, "root-path": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\src\\lib", "root": "username/hello", "rel": "lib", "files": {"E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\src\\lib\\hello.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}}, "wbtest-files": {}, "test-files": {"E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\src\\lib\\hello_test.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}}, "mbt-md-files": {}, "deps": [], "wbtest-deps": [], "test-deps": [], "artifact": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\target\\wasm-gc\\release\\check\\lib\\lib.mi"}, {"is-main": true, "is-third-party": false, "root-path": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\src\\main", "root": "username/hello", "rel": "main", "files": {"E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\src\\main\\main.mbt": {"backend": ["<PERSON><PERSON>", "WasmGC", "Js", "Native", "LLVM"], "optlevel": ["Debug", "Release"]}}, "wbtest-files": {}, "test-files": {}, "mbt-md-files": {}, "deps": [], "wbtest-deps": [], "test-deps": [], "artifact": "E:\\github\\web2\\shylockliu555.github.io\\浏览器自动化\\test-project\\target\\wasm-gc\\release\\check\\main\\main.mi"}], "deps": [], "backend": "wasm-gc", "opt_level": "release", "source": "src"}