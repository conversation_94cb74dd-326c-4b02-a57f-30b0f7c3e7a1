// 简化的MoonBit测试文件

// 基本数据类型
struct SimpleModel {
  progress : Int
  title : String
}

// 初始化函数
fn init_simple_model() -> SimpleModel {
  { progress: 0, title: "MoonBit Website Guide" }
}

// 计算函数
fn calculate_cost(domain_cost : Double, server_cost : Double) -> Double {
  domain_cost + server_cost * 12.0
}

// 主函数
fn main {
  let model = init_simple_model()
  println("=== MoonBit Website Guide ===")
  println("Title: " + model.title)
  println("Progress: " + model.progress.to_string() + "%")
  
  let annual_cost = calculate_cost(10.0, 58.0)
  println("Annual Cost: $" + annual_cost.to_string())
  
  println("=== Test Completed ===")
}
