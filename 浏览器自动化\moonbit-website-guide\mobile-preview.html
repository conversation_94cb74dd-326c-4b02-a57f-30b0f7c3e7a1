<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoonBit 网站指南 - 移动版</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 10px;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
            padding: 20px;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .moonbit-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        .moonbit-badge i {
            margin-right: 6px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-header i {
            font-size: 1.3rem;
            margin-right: 10px;
            color: #667eea;
        }

        .card-header h3 {
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 12px;
            transition: width 0.8s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .phase-indicators {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }

        .phase-indicator {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0 2px;
        }

        .phase-indicator.active {
            background: #667eea;
            color: white;
        }

        .phase-indicator.inactive {
            background: #f0f0f0;
            color: #999;
        }

        .calculator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .input-group {
            margin-bottom: 12px;
        }

        .input-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: bold;
            color: #555;
            font-size: 0.9rem;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .result {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-size: 1rem;
            font-weight: bold;
            margin-top: 10px;
        }

        .phase-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }

        .phase-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .target {
            background: #e8f4fd;
            padding: 8px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #1976d2;
            font-size: 0.9rem;
        }

        .tools-list {
            list-style: none;
        }

        .tools-list li {
            padding: 6px 0;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .tools-list li i {
            margin-right: 8px;
            color: #667eea;
            width: 16px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            margin: 5px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .comparison-simple {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .comparison-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            font-size: 0.9rem;
        }

        .comparison-item:last-child {
            border-bottom: none;
        }

        .check-icon {
            color: #4CAF50;
            font-weight: bold;
        }

        .cross-icon {
            color: #f44336;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            padding: 20px;
        }

        .footer h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .footer p {
            font-size: 0.9rem;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> MoonBit 网站指南</h1>
            <div class="moonbit-badge">
                <i class="fas fa-moon"></i>
                <span>函数式编程实现</span>
            </div>
        </div>

        <!-- 进度跟踪 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i>
                <h3>实施进度</h3>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 33%">33%</div>
            </div>
            <div class="phase-indicators">
                <span class="phase-indicator active">阶段一</span>
                <span class="phase-indicator inactive">阶段二</span>
                <span class="phase-indicator inactive">阶段三</span>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn btn-small" onclick="nextPhase()">下一阶段</button>
                <button class="btn btn-small" onclick="resetProgress()">重置</button>
            </div>
        </div>

        <!-- 成本计算器 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calculator"></i>
                <h3>成本计算器</h3>
            </div>
            <div class="calculator">
                <div class="input-group">
                    <label for="domainCost">域名费用（￥/年）</label>
                    <input type="number" id="domainCost" value="10" min="1" onchange="calculateCost()">
                </div>
                <div class="input-group">
                    <label for="serverType">服务器类型</label>
                    <select id="serverType" onchange="calculateCost()">
                        <option value="0">静态网站 (免费)</option>
                        <option value="58">轻量服务器 (￥58/月)</option>
                    </select>
                </div>
                <div class="result" id="costResult">年度总成本: ￥10</div>
            </div>
        </div>

        <!-- 收入预估器 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill-wave"></i>
                <h3>收入预估器</h3>
            </div>
            <div class="calculator">
                <div class="input-group">
                    <label for="dailyVisitors">日均访问量</label>
                    <input type="number" id="dailyVisitors" value="500" min="1" onchange="calculateIncome()">
                </div>
                <div class="input-group">
                    <label for="clickRate">点击率（%）</label>
                    <input type="number" id="clickRate" value="1" min="0.1" max="10" step="0.1" onchange="calculateIncome()">
                </div>
                <div class="input-group">
                    <label for="clickValue">点击价值（￥）</label>
                    <input type="number" id="clickValue" value="1" min="0.1" step="0.1" onchange="calculateIncome()">
                </div>
                <div class="result" id="incomeResult">预计月收入: ￥150</div>
            </div>
        </div>

        <!-- 三阶段指南 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-road"></i>
                <h3>三阶段指南</h3>
            </div>

            <div class="phase-card">
                <h4><i class="fas fa-play-circle"></i> 阶段一：快速搭建（72小时）</h4>
                <div class="target">目标：完成基础框架搭建并上线</div>
                <ul class="tools-list">
                    <li><i class="fas fa-tools"></i> Wix（拖拽式操作）</li>
                    <li><i class="fab fa-wordpress"></i> WordPress（插件丰富）</li>
                    <li><i class="fab fa-github"></i> GitHub Pages（免费托管）</li>
                </ul>
            </div>

            <div class="phase-card">
                <h4><i class="fas fa-chart-line"></i> 阶段二：功能迭代（30天）</h4>
                <div class="target">目标：实现现金流覆盖运营成本</div>
                <ul class="tools-list">
                    <li><i class="fas fa-search"></i> Chrome开发者工具</li>
                    <li><i class="fas fa-shopping-cart"></i> WooCommerce</li>
                    <li><i class="fas fa-chart-bar"></i> Google Analytics</li>
                </ul>
            </div>

            <div class="phase-card">
                <h4><i class="fas fa-robot"></i> 阶段三：自动化运维</h4>
                <div class="target">目标：节省50%运维时间，AI赋能</div>
                <ul class="tools-list">
                    <li><i class="fab fa-gitlab"></i> GitLab CI</li>
                    <li><i class="fas fa-heartbeat"></i> UptimeRobot</li>
                    <li><i class="fas fa-comments"></i> Tidio + ChatGPT</li>
                </ul>
            </div>
        </div>

        <!-- 技术对比 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-balance-scale"></i>
                <h3>技术对比</h3>
            </div>
            <div class="comparison-simple">
                <div class="comparison-item">
                    <span><strong>类型安全</strong></span>
                    <span><span class="cross-icon">❌</span> HTML → <span class="check-icon">✅</span> MoonBit</span>
                </div>
                <div class="comparison-item">
                    <span><strong>状态管理</strong></span>
                    <span><span class="cross-icon">❌</span> 可变 → <span class="check-icon">✅</span> 不可变</span>
                </div>
                <div class="comparison-item">
                    <span><strong>错误处理</strong></span>
                    <span><span class="cross-icon">❌</span> 运行时 → <span class="check-icon">✅</span> 编译时</span>
                </div>
                <div class="comparison-item">
                    <span><strong>代码维护</strong></span>
                    <span><span class="cross-icon">❌</span> 耦合 → <span class="check-icon">✅</span> 模块化</span>
                </div>
                <div class="comparison-item">
                    <span><strong>性能</strong></span>
                    <span><span class="cross-icon">❌</span> 解释 → <span class="check-icon">✅</span> 编译优化</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <h3><i class="fas fa-star"></i> MoonBit 优势</h3>
            <p>函数式编程带来更安全、可靠、易维护的代码</p>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="window.open('https://www.moonbitlang.com/', '_blank')">
                    <i class="fas fa-external-link-alt"></i> 了解 MoonBit
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentPhase = 1;
        let currentProgress = 33;

        function nextPhase() {
            if (currentPhase < 3) {
                currentPhase++;
                currentProgress = currentPhase * 33;
                updateProgress();
            }
        }

        function resetProgress() {
            currentPhase = 1;
            currentProgress = 33;
            updateProgress();
        }

        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const indicators = document.querySelectorAll('.phase-indicator');

            progressFill.style.width = currentProgress + '%';
            progressFill.textContent = currentProgress + '%';

            indicators.forEach((indicator, index) => {
                if (index < currentPhase) {
                    indicator.className = 'phase-indicator active';
                } else {
                    indicator.className = 'phase-indicator inactive';
                }
            });
        }

        function calculateCost() {
            const domainCost = parseFloat(document.getElementById('domainCost').value);
            const serverType = document.getElementById('serverType');
            const serverCost = parseFloat(serverType.options[serverType.selectedIndex].value);
            const annualCost = domainCost + (serverCost * 12);
            document.getElementById('costResult').textContent = '年度总成本: ￥' + annualCost.toFixed(2);
        }

        function calculateIncome() {
            const visitors = parseFloat(document.getElementById('dailyVisitors').value);
            const clickRate = parseFloat(document.getElementById('clickRate').value) / 100;
            const clickValue = parseFloat(document.getElementById('clickValue').value);
            const monthlyIncome = visitors * clickRate * clickValue * 30;
            document.getElementById('incomeResult').textContent = '预计月收入: ￥' + monthlyIncome.toFixed(2);
        }

        document.addEventListener('DOMContentLoaded', function() {
            calculateCost();
            calculateIncome();
            updateProgress();
        });
    </script>
</body>
</html>
