<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoonBit 网站建设指南 - 交互式预览</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .moonbit-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 10px;
            backdrop-filter: blur(10px);
        }

        .moonbit-badge i {
            margin-right: 8px;
            font-size: 1.2rem;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-header i {
            font-size: 1.5rem;
            margin-right: 12px;
            color: #667eea;
        }

        .card-header h3 {
            color: #2c3e50;
            font-size: 1.3rem;
        }

        .progress-section {
            grid-column: 1 / -1;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            overflow: hidden;
            margin: 15px 0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 15px;
            transition: width 0.8s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .phase-indicators {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .phase-indicator {
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .phase-indicator.active {
            background: #667eea;
            color: white;
        }

        .phase-indicator.inactive {
            background: #f0f0f0;
            color: #999;
        }

        .calculator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .result {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 15px;
        }

        .phases-section {
            grid-column: 1 / -1;
            margin-top: 20px;
        }

        .phases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .phase-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .phase-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .phase-card .target {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #1976d2;
        }

        .tools-list {
            list-style: none;
        }

        .tools-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .tools-list li:last-child {
            border-bottom: none;
        }

        .tools-list li i {
            margin-right: 10px;
            color: #667eea;
        }

        .comparison-section {
            grid-column: 1 / -1;
            margin-top: 30px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }

        .comparison-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .check-icon {
            color: #4CAF50;
            font-weight: bold;
        }

        .cross-icon {
            color: #f44336;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            padding: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .phases-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> 零基础快速搭建独立网站指南</h1>
            <div class="moonbit-badge">
                <i class="fas fa-moon"></i>
                <span>Powered by MoonBit - 函数式编程重新实现</span>
            </div>
        </div>

        <div class="dashboard">
            <!-- 进度跟踪 -->
            <div class="card progress-section">
                <div class="card-header">
                    <i class="fas fa-chart-line"></i>
                    <h3>实施进度跟踪</h3>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 33%">33%</div>
                </div>
                <div class="phase-indicators">
                    <span class="phase-indicator active">阶段一</span>
                    <span class="phase-indicator inactive">阶段二</span>
                    <span class="phase-indicator inactive">阶段三</span>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn" onclick="nextPhase()">下一阶段</button>
                    <button class="btn" onclick="resetProgress()" style="margin-left: 10px;">重置进度</button>
                </div>
            </div>

            <!-- 成本计算器 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-calculator"></i>
                    <h3>成本计算器</h3>
                </div>
                <div class="calculator">
                    <div class="input-group">
                        <label for="domainCost">域名费用（￥/年）</label>
                        <input type="number" id="domainCost" value="10" min="1" onchange="calculateCost()">
                    </div>
                    <div class="input-group">
                        <label for="serverType">服务器类型</label>
                        <select id="serverType" onchange="calculateCost()">
                            <option value="0">静态网站 (GitHub Pages - 免费)</option>
                            <option value="58">阿里云轻量应用服务器 (￥58/月)</option>
                        </select>
                    </div>
                    <div class="result" id="costResult">年度总成本: ￥10</div>
                </div>
            </div>

            <!-- 收入预估器 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-money-bill-wave"></i>
                    <h3>收入预估器</h3>
                </div>
                <div class="calculator">
                    <div class="input-group">
                        <label for="dailyVisitors">日均访问量</label>
                        <input type="number" id="dailyVisitors" value="500" min="1" onchange="calculateIncome()">
                    </div>
                    <div class="input-group">
                        <label for="clickRate">广告点击率（%）</label>
                        <input type="number" id="clickRate" value="1" min="0.1" max="10" step="0.1" onchange="calculateIncome()">
                    </div>
                    <div class="input-group">
                        <label for="clickValue">单次点击价值（￥）</label>
                        <input type="number" id="clickValue" value="1" min="0.1" step="0.1" onchange="calculateIncome()">
                    </div>
                    <div class="result" id="incomeResult">预计月收入: ￥150</div>
                </div>
            </div>
        </div>

        <!-- 三阶段实施指南 -->
        <div class="card phases-section">
            <div class="card-header">
                <i class="fas fa-road"></i>
                <h3>三阶段实施指南</h3>
            </div>
            <div class="phases-grid">
                <div class="phase-card">
                    <h4><i class="fas fa-play-circle"></i> 阶段一：快速搭建临时网站（MVP上线）</h4>
                    <div class="target">目标：72小时内完成基础框架搭建并上线</div>
                    <ul class="tools-list">
                        <li><i class="fas fa-tools"></i> Wix（拖拽式操作，无需技术基础）</li>
                        <li><i class="fas fa-wordpress"></i> WordPress（插件生态丰富）</li>
                        <li><i class="fab fa-github"></i> GitHub Pages（免费静态网站托管）</li>
                        <li><i class="fas fa-code"></i> HTML5 UP（响应式设计模板）</li>
                    </ul>
                </div>

                <div class="phase-card">
                    <h4><i class="fas fa-chart-line"></i> 阶段二：功能迭代与盈利闭环</h4>
                    <div class="target">目标：30天内实现现金流覆盖运营成本</div>
                    <ul class="tools-list">
                        <li><i class="fas fa-search"></i> Chrome开发者工具（实时调试）</li>
                        <li><i class="fas fa-shopping-cart"></i> WooCommerce（电商功能）</li>
                        <li><i class="fas fa-chart-bar"></i> Google Analytics（数据分析）</li>
                        <li><i class="fas fa-dollar-sign"></i> Stripe/PayPal（支付集成）</li>
                    </ul>
                </div>

                <div class="phase-card">
                    <h4><i class="fas fa-robot"></i> 阶段三：自动化运维与AI赋能</h4>
                    <div class="target">目标：节省50%运维时间，引入AI提升用户体验</div>
                    <ul class="tools-list">
                        <li><i class="fab fa-gitlab"></i> GitLab CI（自动化部署）</li>
                        <li><i class="fas fa-heartbeat"></i> UptimeRobot（网站监控）</li>
                        <li><i class="fas fa-comments"></i> Tidio + ChatGPT（智能客服）</li>
                        <li><i class="fas fa-brain"></i> Jasper.ai（AI内容生成）</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术对比 -->
        <div class="card comparison-section">
            <div class="card-header">
                <i class="fas fa-balance-scale"></i>
                <h3>原版HTML vs MoonBit版本对比</h3>
            </div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>原版HTML</th>
                        <th>MoonBit版本</th>
                        <th>优势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>类型安全</strong></td>
                        <td><span class="cross-icon">❌</span> 运行时错误</td>
                        <td><span class="check-icon">✅</span> 编译时检查</td>
                        <td>避免类型相关的bug</td>
                    </tr>
                    <tr>
                        <td><strong>状态管理</strong></td>
                        <td><span class="cross-icon">❌</span> 可变全局变量</td>
                        <td><span class="check-icon">✅</span> 不可变结构</td>
                        <td>更可预测的状态变化</td>
                    </tr>
                    <tr>
                        <td><strong>错误处理</strong></td>
                        <td><span class="cross-icon">❌</span> 异常处理</td>
                        <td><span class="check-icon">✅</span> 类型系统保证</td>
                        <td>编译时发现潜在问题</td>
                    </tr>
                    <tr>
                        <td><strong>代码维护</strong></td>
                        <td><span class="cross-icon">❌</span> 耦合度高</td>
                        <td><span class="check-icon">✅</span> 模块化设计</td>
                        <td>易于扩展和重构</td>
                    </tr>
                    <tr>
                        <td><strong>性能</strong></td>
                        <td><span class="cross-icon">❌</span> 运行时解释</td>
                        <td><span class="check-icon">✅</span> 编译优化</td>
                        <td>更快的执行速度</td>
                    </tr>
                    <tr>
                        <td><strong>开发效率</strong></td>
                        <td><span class="cross-icon">❌</span> 手动调试</td>
                        <td><span class="check-icon">✅</span> IDE支持</td>
                        <td>智能提示和错误检查</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <h3><i class="fas fa-star"></i> MoonBit 函数式编程的优势</h3>
            <p>通过函数式编程范式，我们获得了更安全、更可靠、更易维护的代码，</p>
            <p>同时保持了原版的所有功能和用户体验。</p>
            <div style="margin-top: 20px;">
                <button class="btn" onclick="window.open('https://www.moonbitlang.com/', '_blank')">
                    <i class="fas fa-external-link-alt"></i> 了解更多 MoonBit
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentPhase = 1;
        let currentProgress = 33;

        // 进度管理
        function nextPhase() {
            if (currentPhase < 3) {
                currentPhase++;
                currentProgress = currentPhase * 33;
                updateProgress();
            }
        }

        function resetProgress() {
            currentPhase = 1;
            currentProgress = 33;
            updateProgress();
        }

        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const indicators = document.querySelectorAll('.phase-indicator');

            progressFill.style.width = currentProgress + '%';
            progressFill.textContent = currentProgress + '%';

            indicators.forEach((indicator, index) => {
                if (index < currentPhase) {
                    indicator.className = 'phase-indicator active';
                } else {
                    indicator.className = 'phase-indicator inactive';
                }
            });
        }

        // 成本计算器
        function calculateCost() {
            const domainCost = parseFloat(document.getElementById('domainCost').value);
            const serverType = document.getElementById('serverType');
            const serverCost = parseFloat(serverType.options[serverType.selectedIndex].value);
            const annualCost = domainCost + (serverCost * 12);
            document.getElementById('costResult').textContent = '年度总成本: ￥' + annualCost.toFixed(2);
        }

        // 收入计算器
        function calculateIncome() {
            const visitors = parseFloat(document.getElementById('dailyVisitors').value);
            const clickRate = parseFloat(document.getElementById('clickRate').value) / 100;
            const clickValue = parseFloat(document.getElementById('clickValue').value);
            const monthlyIncome = visitors * clickRate * clickValue * 30;
            document.getElementById('incomeResult').textContent = '预计月收入: ￥' + monthlyIncome.toFixed(2);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            calculateCost();
            calculateIncome();
            updateProgress();

            // 添加一些动画效果
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
