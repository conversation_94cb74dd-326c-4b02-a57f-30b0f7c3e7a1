
         --  -- 
       /  //  / __--------_
      /  //  /_/            \
   ---      -                \ __
  / X        /        ____   /   )
  *_________/__/_____/______/ `--

Oops, the compiler has encountered an unexpected situation.
This is a bug in the compiler.

A bug report containing the error description and relevant code would be
greatly appreciated. You can submit the bug report here:

  https://github.com/moonbitlang/moonbit-docs/issues/new?template=ice.md

Error: Sys_error("E:\\github\\web2\\shylockliu555.github.io\\\231\152\154\238\144\180??\229\136\187\239\138\174?\229\133\184?: Invalid argument")

Compiler args: moonc build-package -error-format json "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\test-project\\src\\main\\main.mbt" -o "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\test-project\\target\\wasm-gc\\release\\build\\main\\main.core" -pkg username/hello/main -is-main -std-path "C:\\Users\\<USER>\\.moon\\lib\\core\\target\\wasm-gc\\release\\bundle" -pkg-sources "username/hello/main:E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\test-project\\src\\main" -target wasm-gc

moonc version: v0.1.20250606+a3f4966ca
