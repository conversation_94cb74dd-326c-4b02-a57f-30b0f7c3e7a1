
         --  -- 
       /  //  / __--------_
      /  //  /_/            \
   ---      -                \ __
  / X        /        ____   /   )
  *_________/__/_____/______/ `--

Oops, the compiler has encountered an unexpected situation.
This is a bug in the compiler.

A bug report containing the error description and relevant code would be
greatly appreciated. You can submit the bug report here:

  https://github.com/moonbitlang/moonbit-docs/issues/new?template=ice.md

Error: Sys_error("E:\\github\\web2\\shylockliu555.github.io\\\231\152\154\238\144\180??\229\136\187\239\138\174?\229\133\184?: Invalid argument")

Compiler args: moonc check -error-format json "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\moonbit-website-guide\\main.mbt" "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\moonbit-website-guide\\simple.mbt" "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\moonbit-website-guide\\test.mbt" -o "E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\moonbit-website-guide\\target\\wasm-gc\\release\\check\\moonbit-website-guide.mi" -pkg moonbit-website-guide -is-main -std-path "C:\\Users\\<USER>\\.moon\\lib\\core\\target\\wasm-gc\\release\\bundle" -pkg-sources "moonbit-website-guide:E:\\github\\web2\\shylockliu555.github.io\\瘚??刻?典?\\moonbit-website-guide" -target wasm-gc

moonc version: v0.1.20250606+a3f4966ca
