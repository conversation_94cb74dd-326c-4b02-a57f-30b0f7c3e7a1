// 测试文件 - 验证核心功能

// 简单的测试函数
fn test_model_creation() {
  let model = init_model()
  println("模型创建成功:")
  println("  进度: " + model.progress.to_string())
  println("  当前阶段: " + model.current_phase.to_string())
  println("  域名成本: " + model.calculator_state.domain_cost.to_string())
}

fn test_cost_calculation() {
  let model = init_model()
  let annual_cost = calculate_annual_cost(model.calculator_state)
  println("成本计算测试:")
  println("  年度总成本: ￥" + annual_cost.to_string())
}

fn test_income_calculation() {
  let model = init_model()
  let monthly_income = calculate_monthly_income(model.calculator_state)
  println("收入计算测试:")
  println("  预计月收入: ￥" + monthly_income.to_string())
}

fn test_phase_data() {
  let phases = get_phases()
  println("阶段数据测试:")
  println("  总阶段数: " + phases.length().to_string())
  
  for i = 0; i < phases.length(); i = i + 1 {
    let phase = phases[i]
    println("  阶段" + (i + 1).to_string() + ": " + phase.title)
    println("    目标: " + phase.target)
    println("    章节数: " + phase.sections.length().to_string())
  }
}

fn test_html_generation() {
  let model = init_model()
  let html_content = view(model)
  println("HTML生成测试:")
  println("  HTML标签: " + html_content.tag)
  println("  HTML文本: " + html_content.text)
}

// 运行所有测试
fn run_tests() {
  println("=== MoonBit 网站指南测试 ===")
  println("")
  
  test_model_creation()
  println("")
  
  test_cost_calculation()
  println("")
  
  test_income_calculation()
  println("")
  
  test_phase_data()
  println("")
  
  test_html_generation()
  println("")
  
  println("=== 测试完成 ===")
}
