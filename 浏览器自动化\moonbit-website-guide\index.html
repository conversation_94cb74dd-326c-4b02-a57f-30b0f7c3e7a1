<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoonBit 网站建设指南 - 预览导航</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }

        .header {
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .moonbit-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            padding: 12px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            font-size: 1.1rem;
        }

        .moonbit-badge i {
            margin-right: 10px;
            font-size: 1.3rem;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .preview-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .preview-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .preview-card .icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .preview-card h3 {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .preview-card .description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .preview-card .features {
            list-style: none;
            text-align: left;
            margin-bottom: 25px;
        }

        .preview-card .features li {
            padding: 5px 0;
            color: #555;
            display: flex;
            align-items: center;
        }

        .preview-card .features li i {
            color: #4CAF50;
            margin-right: 10px;
            width: 16px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            color: white;
            backdrop-filter: blur(10px);
        }

        .stat-item .number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .stat-item .label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .footer {
            color: white;
            margin-top: 50px;
        }

        .footer p {
            opacity: 0.8;
            line-height: 1.6;
        }

        .footer .links {
            margin-top: 20px;
        }

        .footer .links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .footer .links a:hover {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .preview-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> MoonBit 网站建设指南</h1>
            <div class="subtitle">零基础快速搭建独立网站的三阶段实施方案</div>
            <div class="moonbit-badge">
                <i class="fas fa-moon"></i>
                <span>函数式编程重新实现</span>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <span class="number">3</span>
                <span class="label">预览版本</span>
            </div>
            <div class="stat-item">
                <span class="number">100%</span>
                <span class="label">类型安全</span>
            </div>
            <div class="stat-item">
                <span class="number">0</span>
                <span class="label">运行时错误</span>
            </div>
            <div class="stat-item">
                <span class="number">∞</span>
                <span class="label">可维护性</span>
            </div>
        </div>

        <div class="preview-grid">
            <!-- 桌面版预览 -->
            <div class="preview-card" onclick="window.open('preview.html', '_blank')">
                <div class="icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <h3>桌面版预览</h3>
                <div class="description">
                    完整功能的现代化界面，展示MoonBit函数式编程的所有优势
                </div>
                <ul class="features">
                    <li><i class="fas fa-check"></i> 交互式进度跟踪</li>
                    <li><i class="fas fa-check"></i> 实时计算器</li>
                    <li><i class="fas fa-check"></i> 详细技术对比</li>
                    <li><i class="fas fa-check"></i> 三阶段完整指南</li>
                    <li><i class="fas fa-check"></i> 动画效果</li>
                </ul>
                <a href="preview.html" class="btn" onclick="event.stopPropagation()">
                    <i class="fas fa-external-link-alt"></i> 打开桌面版
                </a>
            </div>

            <!-- 移动版预览 -->
            <div class="preview-card" onclick="window.open('mobile-preview.html', '_blank')">
                <div class="icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3>移动版预览</h3>
                <div class="description">
                    专为移动设备优化的响应式界面，保持核心功能的同时提供最佳体验
                </div>
                <ul class="features">
                    <li><i class="fas fa-check"></i> 响应式设计</li>
                    <li><i class="fas fa-check"></i> 触摸友好</li>
                    <li><i class="fas fa-check"></i> 简化界面</li>
                    <li><i class="fas fa-check"></i> 核心功能</li>
                    <li><i class="fas fa-check"></i> 快速加载</li>
                </ul>
                <a href="mobile-preview.html" class="btn" onclick="event.stopPropagation()">
                    <i class="fas fa-mobile-alt"></i> 打开移动版
                </a>
            </div>

            <!-- 原始版本 -->
            <div class="preview-card" onclick="window.open('output.html', '_blank')">
                <div class="icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3>原始版本</h3>
                <div class="description">
                    基础HTML实现版本，用于对比展示MoonBit版本的改进之处
                </div>
                <ul class="features">
                    <li><i class="fas fa-check"></i> 静态内容</li>
                    <li><i class="fas fa-check"></i> 基础样式</li>
                    <li><i class="fas fa-check"></i> 兼容性好</li>
                    <li><i class="fas fa-check"></i> 对比参考</li>
                    <li><i class="fas fa-check"></i> 轻量级</li>
                </ul>
                <a href="output.html" class="btn" onclick="event.stopPropagation()">
                    <i class="fas fa-file-code"></i> 打开原始版
                </a>
            </div>
        </div>

        <div class="footer">
            <h3><i class="fas fa-star"></i> MoonBit 函数式编程的优势</h3>
            <p>
                通过函数式编程范式，我们获得了更安全、更可靠、更易维护的代码，<br>
                同时保持了原版的所有功能和用户体验。
            </p>
            <div class="links">
                <a href="https://www.moonbitlang.com/" target="_blank">
                    <i class="fas fa-external-link-alt"></i> MoonBit 官网
                </a>
                <a href="README.md" target="_blank">
                    <i class="fas fa-book"></i> 项目文档
                </a>
                <a href="COMPARISON.md" target="_blank">
                    <i class="fas fa-balance-scale"></i> 技术对比
                </a>
                <a href="PREVIEW_GUIDE.md" target="_blank">
                    <i class="fas fa-guide"></i> 使用指南
                </a>
            </div>
        </div>
    </div>

    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.preview-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
