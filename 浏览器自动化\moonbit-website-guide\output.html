<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零基础快速搭建独立网站指南 - MoonBit版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .header h1 {
            text-align: center;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .progress-container {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            background-color: #e0e0e0;
            border-radius: 5px;
            height: 25px;
        }
        .progress {
            height: 100%;
            background-color: #4CAF50;
            border-radius: 5px;
            text-align: center;
            line-height: 25px;
            color: white;
            width: 33%;
        }
        .phase {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .phase-header {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .collapsible {
            background-color: #f1f1f1;
            color: #444;
            cursor: pointer;
            padding: 18px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 16px;
            border-radius: 5px;
            margin-bottom: 10px;
            transition: 0.4s;
        }
        .collapsible:hover {
            background-color: #e0e0e0;
        }
        .content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.2s ease-out;
            background-color: white;
        }
        .content.expanded {
            max-height: 1000px;
            padding: 18px;
        }
        .tool-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
        }
        .tool-card h3 {
            color: #3498db;
            margin-top: 0;
        }
        .calculator {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .calculator-row {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .calculator-result {
            font-weight: bold;
            color: #2c3e50;
            font-size: 18px;
            margin-top: 20px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
        }
        .navigation button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .navigation button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .tabs-container {
            margin: 20px 0;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        .tab-button {
            background-color: #f1f1f1;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
        }
        .tab-button.active {
            background-color: #3498db;
            color: white;
        }
        .moonbit-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #6c5ce7;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="moonbit-badge">
        🌙 Powered by MoonBit
    </div>
    
    <div class="container">
        <div class="header">
            <h1>零基础快速搭建独立网站的三阶段实施方案</h1>
            <p style="text-align: center; color: #7f8c8d;">
                本指南使用 MoonBit 编程语言重新实现，展示函数式编程在 Web 开发中的应用
            </p>
        </div>
        
        <div class="progress-container">
            <h3>实施进度跟踪</h3>
            <div class="progress-bar">
                <div class="progress" id="progressBar">33%</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                <span>阶段一</span>
                <span>阶段二</span>
                <span>阶段三</span>
            </div>
        </div>

        <div class="phase">
            <div class="phase-header">
                阶段一：快速搭建临时网站（MVP上线）
            </div>
            <p><strong>目标：72小时内完成基础框架搭建并上线</strong></p>

            <button class="collapsible" onclick="toggleSection('tools_selection')">
                1. 工具选择与模板获取
            </button>
            <div class="content" id="tools_selection">
                <div class="tabs-container">
                    <div class="tabs">
                        <button class="tab-button active" onclick="switchTab('wix')">Wix</button>
                        <button class="tab-button" onclick="switchTab('wordpress')">WordPress</button>
                        <button class="tab-button" onclick="switchTab('templates')">免费模板</button>
                    </div>
                    <div class="tab-content">
                        <div class="tool-card" id="wix">
                            <h3>Wix优势</h3>
                            <p>无需技术基础的网站构建平台</p>
                            <ul>
                                <li>无需技术基础，拖拽式操作</li>
                                <li>内置200+免费模板</li>
                                <li>支持多语言切换（适合国际化场景）</li>
                            </ul>
                            <button onclick="window.open('https://www.wix.com', '_blank')">访问链接</button>
                        </div>
                    </div>
                </div>
            </div>

            <button class="collapsible" onclick="toggleSection('domain_server')">
                2. 域名与服务器最低成本方案
            </button>
            <div class="content" id="domain_server">
                <div class="calculator">
                    <h3>域名服务器成本计算器</h3>
                    <div class="calculator-row">
                        <label>域名费用（￥/年）:</label>
                        <input type="number" id="domainCost" value="10" min="1" onchange="calculateCost()">
                    </div>
                    <div class="calculator-row">
                        <label>服务器类型:</label>
                        <select id="serverType" onchange="calculateCost()">
                            <option value="0">静态网站 (GitHub Pages - 免费)</option>
                            <option value="58">阿里云轻量应用服务器 (￥58/月)</option>
                        </select>
                    </div>
                    <div class="calculator-result" id="costResult">
                        年度总成本: ￥10
                    </div>
                </div>
            </div>

            <button class="collapsible" onclick="toggleSection('quick_launch')">
                3. 快速上线操作流
            </button>
            <div class="content" id="quick_launch">
                <ol>
                    <li>注册域名并解析至服务器IP（DNS配置约10分钟生效）</li>
                    <li>通过FTP（FileZilla）或控制面板上传模板文件</li>
                    <li>修改模板关键信息（Logo/文案/联系方式）- 使用VS Code全局替换功能</li>
                    <li>安装基础插件（WordPress需配置SEO插件Yoast）</li>
                    <li>本地测试后通过GitHub Actions或手动上传至服务器</li>
                </ol>
            </div>
        </div>

        <div class="navigation">
            <button onclick="prevPhase()" disabled>上一阶段</button>
            <div class="phase-indicator">当前阶段: 1/3</div>
            <button onclick="nextPhase()">下一阶段</button>
        </div>
    </div>

    <script>
        // MoonBit 生成的交互逻辑
        let currentPhase = 1;
        let expandedSections = [];

        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId);
            const isExpanded = expandedSections.includes(sectionId);
            
            if (isExpanded) {
                content.classList.remove('expanded');
                expandedSections = expandedSections.filter(id => id !== sectionId);
            } else {
                content.classList.add('expanded');
                expandedSections.push(sectionId);
            }
        }

        function switchTab(tabId) {
            console.log('Switch tab:', tabId);
            // 实际实现中会切换标签页内容
        }

        function nextPhase() {
            if (currentPhase < 3) {
                currentPhase++;
                updateProgress();
                // 实际实现中会切换阶段内容
            }
        }

        function prevPhase() {
            if (currentPhase > 1) {
                currentPhase--;
                updateProgress();
                // 实际实现中会切换阶段内容
            }
        }

        function updateProgress() {
            const progress = currentPhase * 33;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressBar').textContent = progress + '%';
        }

        function calculateCost() {
            const domainCost = parseFloat(document.getElementById('domainCost').value);
            const serverType = document.getElementById('serverType');
            const serverCost = parseFloat(serverType.options[serverType.selectedIndex].value);
            const annualCost = domainCost + (serverCost * 12);
            document.getElementById('costResult').textContent = '年度总成本: ￥' + annualCost.toFixed(2);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认展开第一个章节
            toggleSection('tools_selection');
        });
    </script>
</body>
</html>
